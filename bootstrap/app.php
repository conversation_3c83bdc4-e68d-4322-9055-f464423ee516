<?php

use App\Billing\Gateway\GatewayException;
use App\Exceptions\BackOrderException;
use App\Exceptions\ProductNotFoundException;
use App\Models\Detour;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Session\TokenMismatchException;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\IdempotencyException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/tenant.php',
        commands: __DIR__.'/../routes/console.php',
        // channels: __DIR__.'/../routes/channels.php',
        health: '/up'
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->redirectGuestsTo(fn () => route('login'));

        $middleware->encryptCookies(except: [
            'address_check',
            'referral_code',
            'last_viewed_page',
            'fulfillment_id',
            'navigation_toggled',
            'cookie_consent'
        ]);
        $middleware->validateCsrfTokens(except: ['horizon/*', 'stripe/*', 'livewire/*']);

        $middleware->throttleWithRedis();

        $middleware->web([
            \App\Http\Middleware\SetTenantContext::class,
            \App\Http\Middleware\SetBugsnagUserContext::class
        ]);

        $middleware->group('tenant', [
            \App\Http\Middleware\SetTenantContext::class,
//            \App\Http\Middleware\RedirectToPreferredDomain::class,
//            \App\Http\Middleware\CheckForTenantMaintenanceMode::class,
        ]);

        $middleware->group('public_api', []);

        $middleware->group('webCsrfExempt', [
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\SetBugsnagUserContext::class,
            \App\Http\Middleware\SetTenantContext::class,
        ]);

        $middleware->group('webhook', [
        ]);

        $middleware->group('css', [
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
        ]);

        $middleware->group('api', [
            // \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \Illuminate\Routing\Middleware\ThrottleRequests::class.':api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            'auth:api',
        ]);

        $middleware->group('horizon', [
            \App\Http\Middleware\AuthenticateHorizon::class,
        ]);

        $middleware->group('pos', [
            \App\Http\Middleware\HandleInertiaRequests::class,
            'auth.admin',
            \App\Http\Middleware\CheckForStripeConnect::class,
        ]);

        $middleware->alias([
            'ForceSeoStoreUrls' => \App\Http\Middleware\ForceSeoStoreUrls::class,
            'UnconfirmedAccount' => \App\Http\Middleware\UnconfirmedAccount::class,
            'admin' => \App\Http\Middleware\Admin::class,
            'auth.admin' => \App\Http\Middleware\AuthAdmin::class,
            'auth.customer' => \App\Http\Middleware\AuthCustomer::class,
            'auth.editor' => \App\Http\Middleware\AuthEditor::class,
            'central.auth' => \App\Http\Middleware\CentralRedirectIfAuthenticated::class,
            'central.guest' => \App\Http\Middleware\CentralRedirectIfAuthenticated::class,
            'detour' => \App\Http\Middleware\DetourMiddleware::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'order' => \App\Http\Middleware\GetOpenOrder::class,
            'track-view' => \App\Http\Middleware\Theme\TrackView::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {

        $exceptions->dontReport([
            ProductNotFoundException::class,
            AuthenticationException::class,
            BackOrderException::class,
            HttpException::class,
            IdempotencyException::class,
            CardException::class,
        ]);

        $exceptions->render(function (Throwable $e, $request) {
            if ($e instanceof NotFoundHttpException) {
                if ($e->getPrevious() instanceof ModelNotFoundException) {
                    $message = 'The item(s) you are requesting could not be found.';
                    if ($request->expectsJson()) {
                        return response()->json([
                            'message' => $message
                        ], 404);
                    }

                    error($message);
                    return back();
                }

                // Check if a redirect exists.
                if ($redirect = (new Detour())->shouldRedirectTraffic($request)) {
                    return $redirect;
                }

                // Display the theme specific error page.
                if (!$request->is('admin/*')) {
                    return response()->view('errors.theme.404', ['exception' => $e], 404);
                }
            }

            if ($e instanceof TokenMismatchException) {
                $message = 'Your session has timed out. Please refresh and try again.';
                if ($request->ajax()) {
                    return response()->json($message, 500);
                } else {
                    error($message);
                    return redirect()->back();
                }
            }

            // Stripe Exceptions
            if ($e instanceof ApiErrorException) {
                $message = $e->getMessage();
                if ($request->ajax()) {
                    return response()->json([
                        'message' => $message
                    ], $e->getHttpStatus());
                }
                error($message);
                return redirect()->back();
            }

            // Gateway Exceptions
            if ($e instanceof GatewayException) {
                $message = $e->getMessage();
                if ($request->expectsJson()) {
                    return response()->json(['message' => $message], 400);
                }

                error($message);

                return redirect()->back();
            }
        });
    })->create();
