<?php

namespace App\Services;

use App\Contracts\Cartable;
use App\Contracts\CartService;
use App\Models\Cart;
use App\Models\User;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Support\Facades\Cookie;

class DatabaseCartService implements CartService
{
    public function create(string $shopper_type, string $shopper_id): Cart
    {
        $cart = new Cart([
            'shopper_type' => $shopper_type,
            'shopper_id' => $shopper_id,
        ]);

        $user = $shopper_type === User::class
            ? User::find($shopper_id)
            : null;

        $delivery_method_id = Cookie::get('shopping_delivery_method_id')
            ?? $user?->pickup_point;

        $shipping = $user?->defaultShippingAttributes();

        if ( ! is_null($shipping)) {
            $delivery_method = app(DeliveryMethodService::class)
                ->find(new GeocodedAddress(
                    lat: 0,
                    lng: 0,
                    city: $shipping['city'] ?? '',
                    state: $shipping['state'] ?? '',
                    postalCode: $shipping['postal_code'] ?? '',
                    country: $shipping['country'] ?? 'USA',
                    accuracy: 1,
                ))
                ->first();

            if (!is_null($delivery_method)) {
                $delivery_method_id = $delivery_method->id;
            }
        }

        $cart->extra_attributes->set([
            'date_id' =>  null,
            'delivery_method_id' => $delivery_method_id,
            'items' => [],
            'subscription' => null,
            'discounts' => [
                'coupons' => [],
                'conditional_coupons' => [],
                'gift_card' =>  [
                    'name' =>  '',
                    'code' =>  '',
                    'amount' =>  0,
                ],
                'store_credit' =>  [
                    'amount' => $user?->credit,
                ]
            ],
            'contact' => [
                'first_name' => $user?->first_name,
                'last_name' => $user?->last_name,
                'email' => $user?->email,
                'phone' => $user?->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => ! is_null($user?->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($user?->subscribed_to_sms_marketing_at),
            ],
            'shipping' => [
                'address_id' => $shipping['address_id'] ?? null,
                'street' => $shipping['street'] ?? '',
                'street_2' => $shipping['street_2'] ?? '',
                'city' => $shipping['city'] ?? '',
                'state' => $shipping['state'] ?? '',
                'zip' => $shipping['postal_code'] ?? '',
                'postal_code' => $shipping['postal_code'] ?? '',
                'country' => 'USA',
                'save_for_later' => true
            ],
            'billing' => [
                'method' => null,
                'source_id' => $user?->checkout_card_id,
                'save_for_later' => true
            ]
        ]);

        $cart->save();

        return $cart;
    }

    public function find(string $shopper_type, string $shopper_id): ?Cart
    {
       $cart = Cart::query()
            ->where([
                'shopper_type' => $shopper_type,
                'shopper_id' => $shopper_id
            ])
           ->first();

        if (! $cart) {
            return null;
        }

        return $this->validateWelcomeCoupon($cart);
    }

    protected function validateWelcomeCoupon(Cart $cart): Cart
    {
        if (
            $cart->shopper_type === User::class
            && $cart->hasConditionalCouponApplied(config('grazecart.welcome_coupon_code'))
            && $cart->cartCustomer()->created_at < today()->subWeeks(2)
        ) {
            $cart->removeConditionalCouponFromCart(config('grazecart.welcome_coupon_code'));
        }

        if ($cart->hasCouponApplied(config('grazecart.welcome_coupon_code')) && $cart->cartSubtotal() < 17500) {
            $cart->removeCouponFromCart(config('grazecart.welcome_coupon_code'));
        }

        return $cart;
    }

    public function findById(string|int $cart_id): ?Cartable
    {
        $cart = Cart::find($cart_id);

        if (! $cart) {
            return null;
        }

        return $this->validateWelcomeCoupon($cart);
    }
}
