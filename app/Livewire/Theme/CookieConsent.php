<?php

namespace App\Livewire\Theme;

use Livewire\Attributes\Locked;
use Livewire\Component;

class CookieConsent extends Component
{
    public bool $showBanner = false;

    #[Locked]
    public string $consentCookieName = 'cookie_consent';

    #[Locked]
    public string $consentCookieValue = 'accepted';

    #[Locked]
    public int $consentCookieExpireDays = 365;

    public function mount()
    {
        // Check if user has already given consent
        $this->showBanner = !$this->hasConsent();
    }

    public function render()
    {
        return view('theme::livewire.cookie-consent');
    }

    public function acceptCookies()
    {
        // Set the consent cookie
        $expires = now()->addDays($this->consentCookieExpireDays);
        
        cookie()->queue(
            $this->consentCookieName,
            $this->consentCookieValue,
            $expires->diffInMinutes(now())
        );

        // Hide the banner
        $this->showBanner = false;

        // Optional: Track analytics event
        $this->js("
            if (typeof gtag !== 'undefined') {
                gtag('event', 'cookie_consent_accepted', {
                    event_category: 'privacy',
                    event_label: 'cookie_banner'
                });
            }
        ");
    }

    private function hasConsent(): bool
    {
        return request()->cookie($this->consentCookieName) === $this->consentCookieValue;
    }
}
