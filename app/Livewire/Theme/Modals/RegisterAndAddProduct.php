<?php

namespace App\Livewire\Theme\Modals;

use App\Contracts\CartService;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserWasRegistered;
use App\Exceptions\NoGeocodeResultsException;
use App\Livewire\Theme\AddsProduct;
use App\Livewire\Theme\FetchesCart;
use App\Models\Coupon;
use App\Models\Lead;
use App\Models\Pickup;
use App\Models\User;
use App\Rules\AllowedEmailDomain;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use App\Support\FetchesGeocodedAddress;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Once;
use Livewire\Attributes\On;
use Livewire\Component;

class RegisterAndAddProduct extends Component
{
    use ModalAttributes, FetchesGeocodedAddress, FetchesCart, AddsProduct;

    public ?int $product_id = null;
    public array $product_metadata = [];

    public string $postal_code = '';

    public string $username = '';

    public ?int $timestamp = null;

    public string $email = '';

    public string $current_url = '';

    public ?string $geocoded_city = null;

    public function mount(): void
    {
        $this->timestamp = time();
        $this->current_url = request()->url();
    }

    public function render()
    {
        return view('theme::livewire.modals.register-and-add-product');
    }

    public function submit()
    {
        $validated = $this->validate([
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime],
            'postal_code' => ['required', 'string', 'max:10'],
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255', 'unique:users,email'],
        ], [
            'email.indisposable' => 'A valid email address is required.',
            'email.unique' => 'The offer is available to new customers only.',
        ]);

        $this->geocoded_city = null;
        $this->postal_code = trim($this->postal_code);

        try {
            $geocoded_address = $this->fetchGeocodedAddressFromPostalCode($this->postal_code);
        } catch (NoGeocodeResultsException $e) {
            $this->addError('postal_code', __('Postal code could not be found.'));
            return;
        }

        /** @var Pickup|null $delivery_method */
        $delivery_method = $this->deliveryMethodForAddress($geocoded_address);

        if (is_null($delivery_method)) {
            $this->addError('postal_code', __('Delivery is not available in this postal code.'));
            return;
        }

        $this->geocoded_city = $geocoded_address->city;

        Cookie::queue(Cookie::make('shopping_city', $this->geocoded_city, 259200));
        Cookie::queue(Cookie::make('shopping_postal_code', $this->postal_code, 259200));
        Cookie::queue(Cookie::make('shopping_delivery_method_id', (string) $delivery_method->id, 259200));

        $validated['city'] = $this->geocoded_city;
        $validated['state'] = $geocoded_address->state;
        $validated['postal_code'] = $this->postal_code;
        $validated['country'] = $delivery_method->id;
        $validated['pickup_point'] = $delivery_method->id;

        $user = $this->handleRegistration(collect($validated));

        // cart created in handleRegistration method
        $cart = $this->fetchShopperCart(should_stub: false);

        if (!is_null($this->product_id)) {
            $this->addToCart($this->product_id, $this->product_metadata);
        }

        Once::flush();

        $this->dispatch('cartDeliveryMethodUpdated');

        session()->flash('open-cart-side-panel');
        session()->flash('userWasCreated', true);

        return $this->redirect($this->current_url ?? request()->url());
    }

    private function deliveryMethodForAddress(GeocodedAddress $address): ?Pickup
    {
        return app(DeliveryMethodService::class)
            ->deliveryZones()
            ->find($address)
            ->first();
    }

    private function handleRegistration(Collection $attributes): User
    {
        if (request()->hasCookie('referral_code')) {
            $attributes->put('referral_code', request()->cookie('referral_code'));
        }

        $user = User::register($attributes);

        Auth::login($user);

        event(new UserSubscribedToNewsletter($user));
        event(new UserWasRegistered($user));

        Lead::where('email', $user->email)->delete();

        $cart = app(CartService::class)
            ->create(shopper_type: User::class, shopper_id: (string) $user->id);

        $cart->applyConditionalCouponToCart(Coupon::welcomeCoupon());

        return $user;
    }

    #[On('close-modal-register-and-add-product')]
    public function close(): void
    {
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-register-and-add-product')]
    public function open(?int $product_id = null, ?array $product_metadata = []): void
    {
        $this->product_id = $product_id;
        $this->product_metadata = $product_metadata;

        $this->openModal();
    }
}
