# Cookie Consent Banner Implementation

## Overview

This implementation adds a cookie consent notification banner to the GrazeCart application to ensure compliance with privacy regulations. The banner informs users about cookie usage and allows them to acknowledge cookies.

## Features

- ✅ Cookie consent banner displayed to first-time visitors
- ✅ User preferences saved in cookies
- ✅ Banner not shown again once preference has been set
- ✅ Responsive and accessible implementation
- ✅ Smooth animations and transitions
- ✅ Privacy Policy link integration

## Implementation Details

### Files Created/Modified

1. **JavaScript Module**: `resources/assets/js/modules/cookieConsent.js`
   - Alpine.js component for cookie consent functionality
   - Handles cookie state management and user interactions

2. **Blade Component**: `resources/views/components/cookie-consent-banner.blade.php`
   - Responsive banner UI with accessibility features
   - Contains the specified message and "Got it!" button

3. **Theme Integration**: `resources/assets/js/theme/theme.js`
   - Registers the cookieConsent Alpine.js component

4. **Layout Updates**:
   - `resources/theme/resources/views/_layouts/main.blade.php`
   - `resources/theme/resources/views/_layouts/checkout.blade.php`
   - `resources/theme/resources/views/_layouts/livewire.blade.php`

5. **Cookie Configuration**: `bootstrap/app.php`
   - Added `cookie_consent` to unencrypted cookies list

6. **Tests**: `tests/Feature/Theme/CookieConsentTest.php`
   - Comprehensive test suite for the cookie consent functionality

### How It Works

1. **First Visit**: When a user visits the site for the first time, the banner appears at the bottom of the screen
2. **User Interaction**: User clicks "Got it!" button to accept cookies
3. **Cookie Storage**: Consent is stored in a `cookie_consent=accepted` cookie that expires in 365 days
4. **Subsequent Visits**: Banner is not shown if consent cookie exists

### Cookie Details

- **Name**: `cookie_consent`
- **Value**: `accepted`
- **Expiration**: 365 days
- **Path**: `/`
- **SameSite**: `Lax`
- **Encryption**: Excluded from Laravel's cookie encryption

### Accessibility Features

- Proper ARIA labels and roles
- Keyboard navigation support
- High contrast colors
- Screen reader friendly
- Focus management

### Responsive Design

- Mobile-first approach
- Flexbox layout that adapts to different screen sizes
- Proper spacing and typography on all devices

### Message Content

The banner displays the following message:

> "We use cookies to make your visit to our farm store even better. By continuing to browse, you're agreeing to our use of cookies. You can read more in our Privacy Policy."

With a "Got it!" button for user acknowledgment.

## Testing

Run the test suite to verify functionality:

```bash
php artisan test tests/Feature/Theme/CookieConsentTest.php
```

The tests cover:
- Banner display on various pages
- Accessibility attributes
- Responsive CSS classes
- Privacy Policy link
- Component registration
- Cookie configuration

## Browser Compatibility

The implementation uses modern web standards and is compatible with:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Privacy Compliance

This implementation helps with:
- GDPR compliance (EU)
- CCPA compliance (California)
- General privacy best practices

The banner provides clear information about cookie usage and allows users to make an informed decision about accepting cookies.
