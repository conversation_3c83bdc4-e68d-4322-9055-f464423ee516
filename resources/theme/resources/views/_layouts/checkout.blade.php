<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="{{ app()->isProduction() ? ($robots ?? 'index,follow') : 'noindex, nofollow' }}">

    <title>{{ $pageTitle ?? request()->segment(1) }} - {{ setting('farm_name', '') }}</title>
    <meta name="description" content="{{ $pageDescription ?? '' }}">
    <link rel="canonical" href="{{ $pageCanonical ?? url()->current() }}"/>
    <meta name="generator" content="GrazeCart">
    <meta name="csrf-token" content="{{ csrf_token() }}" id="csrfToken">
    @if(theme('favicon'))
        <link rel="icon" type="image/png" href="{{ theme('favicon') }}">
    @endif

    {!! setting('site_meta_tags') !!}

    @yield('pageMetaTags')
    {!! getGoogleFontLinks() !!}

    @livewireStyles

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css" integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf" crossorigin="anonymous">

    <link rel="stylesheet" href="/theme/theme-variables.css?id={{ app('theme')->updated_at?->timestamp ?? Str::random(5) }}">

    @vite([
        'resources/theme/resources/assets/css/tailwind-full.css',
        'resources/theme/resources/assets/less/theme.less',
        'resources/assets/js/theme/theme.js',
    ])

    <link rel="stylesheet" href="/theme/theme.css?id={{ $theme->updated_at->timestamp ?? Str::random(5) }}">

    @yield('head')

    @include('theme::_partials.ga-tracking-script')

    @php($fb_pixel_id = config('services.facebook.pixel_id'))
    @includeWhen(!empty($fb_pixel_id), 'theme::_partials.fb-pixel', ['pixel' => $fb_pixel_id])

    {!! setting('header_scripts') !!}
</head>

<body>
<div id="app">
    <a id="top-of-page"></a>

    <!-- Start Site Header -->
    @include('theme::_partials.header.checkout')
    <!-- End Site Header -->

    @yield('steps')
    <!-- Start Site Messages -->
    @include('theme::_partials.errors')
    <!-- End Site Messages -->

    <!-- Start Main Content -->
    @yield('content')
    <!-- End Main Content -->

    <div style="padding: 1rem;">
        @include('theme::_partials.legal-links')
    </div>

    <!-- Start Back To Top Link -->
    <div class="backToTopLink__container">
        <a href="#top-of-page" class="backToTopLink__link"><i class="fa fa-caret-up"></i> {{ __('Return To Top') }}</a>
    </div>
    <!-- End Back To Top Link -->

    <a id="bottom-of-page"></a>

</div>

<!-- Scripts -->
@livewireScriptConfig

<script src="https://js.stripe.com/v3/"></script>

@stack('scripts')

<!-- Cookie Consent Banner -->
<x-cookie-consent-banner />

</body>
</html>
