@php
    $store_service = app(\App\Services\StoreService::class);

    /** @var \App\Models\Order|null $order */
    $order = $store_service->order();

    /** @var \App\Models\Cart $cart */
    $cart = $store_service->cartOrCartStub();

    /** @var \App\Models\Menu $store_menu */
    $store_menu = storeMenu();

    /** @var \App\Models\Menu $main_menu */
    $main_menu = mainMenu();

    $subscription = null;

    /** @var \App\Models\Pickup|null $current_delivery_method */
    $current_delivery_method = $store_service->deliveryMethod(request()->cookie('shopping_delivery_method_id'));

    if (!is_null($order?->blueprint_id) || ($has_subscription ?? false)) {
        $subscription = $store_service->subscription();
    }
@endphp

        <!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="{{ app()->isProduction() ? ($robots ?? 'all') : 'noindex, nofollow' }}">

    <title>{{ $pageTitle ?? request()->segment(1) }} - {{ setting('farm_name', '') }}</title>
    @if(!empty($pageDescription))
        <meta name="description" content="{{ $pageDescription ?? '' }}">
    @endif

    <link rel="canonical" href="{{ $pageCanonical ?? url()->current() }}"/>
    <meta name="generator" content="GrazeCart">
    <meta name="csrf-token" content="{{ csrf_token() }}" id="csrfToken">
    @if(theme('favicon'))
        <link rel="icon" type="image/png" href="{{ theme('favicon') }}">
    @endif
    {!! setting('site_meta_tags') !!}
    @yield('pageMetaTags')

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    {!! getGoogleFontLinks() !!}

    @livewireStyles

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css"
          integrity="sha384-50oBUHEmvpQ+1lW4y57PTFmhCaXp0ML5d60M1M7uH2+nqUivzIebhndOJK28anvf" crossorigin="anonymous">

    <link rel="stylesheet"
          href="/theme/theme-variables.css?id={{ app('theme')->updated_at?->timestamp ?? Str::random(5) }}">

    @vite([
        'resources/theme/resources/assets/css/tailwind-full.css',
        'resources/theme/resources/assets/less/theme.less',
        'resources/assets/js/theme/theme.js',
    ])

    <link rel="stylesheet" href="/theme/theme.css?id=v1.1_{{ $theme->updated_at->timestamp ?? Str::random(5) }}">

    @yield('head')

    @include('theme::_partials.ga-tracking-script')

    @php($fb_pixel_id = config('services.facebook.pixel_id'))
    @includeWhen(!empty($fb_pixel_id), 'theme::_partials.fb-pixel', ['pixel' => $fb_pixel_id])

    {!! setting('header_scripts') !!}

    <style>
        [x-cloak] {
            display: none;
        }
    </style>
</head>

<body
        x-data="{ sidePanelOpened: false }"
        @side-panel-opened.window="sidePanelOpened = true"
        @side-panel-closed.window="setTimeout(() => sidePanelOpened = false, 250)"
>

<div id="app">
    <a id="top-of-page"></a>
    <div class="tw-reset" x-data="topNavigation()">
        <div class="tw-relative tw-z-30">
            @includeWhen(
            (auth()->user()?->hasAdminAccess() ?? false) && ! session('hide_admin_bar'),
            'theme::_partials.admin_toolbar'
        )
            <nav class="tw-bg-gradient-to-r tw-from-zinc-800 tw-to-zinc-700 tw-shadow-inner">
                <div class="tw-mx-auto tw-max-w-7xl tw-px-2 sm:tw-px-4 lg:tw-px-8">
                    <div class="tw-relative tw-flex tw-py-2 tw-items-center tw-justify-between">

                        <div class="tw-flex tw-flex-1 tw-items-center tw-justify-between ">
                            <div class="tw-flex tw-space-x-4">
                                <livewire:theme.delivery-method-toggle
                                        :subscription="$subscription"
                                        :order="$order"
                                        :cart="$cart"
                                />
                            </div>
                            <div>
                                @if(!is_null($order))
                                    <a href="{{ route('customer.orders.show', [$order->id]) }}"
                                       class="tw-no-underline tw-flex">
                                        <p class="tw-m-0 tw-mr-0.5 tw-text-xs tw-text-white">{{ $current_delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }}
                                            on
                                            <span class="tw-font-semibold">{{ $order->pickup_date?->format('M jS') ?? 'TBD' }}</span>
                                        </p>
                                        <span class="tw-relative tw--mt-0.5 tw-flex tw-h-3 tw-w-3">
                                            <span class="tw-animate-ping tw-absolute tw-inline-flex tw-h-full tw-w-full tw-rounded-full tw-bg-theme-action-color/70 tw-opacity-75"></span>
                                            <span class="tw-relative tw-inline-flex tw-rounded-full tw-h-3 tw-w-3 tw-bg-theme-action-color"></span>
                                        </span>
                                    </a>

                                @elseif(!is_null($subscription))
                                    <a href="{{ route('customers.recurring.edit') }}" class="tw-no-underline tw-flex">
                                        <p class="tw-m-0 tw-mr-0.5 tw-text-xs tw-text-white">Scheduled for
                                            <span class="tw-font-semibold">{{ $subscription->readyAtDatetime()?->format('M jS') ?? 'TBD' }}</span>
                                        </p>
                                        <span class="tw-relative tw--mt-0.5 tw-flex tw-h-3 tw-w-3">
                                            <span class="tw-animate-ping tw-absolute tw-inline-flex tw-h-full tw-w-full tw-rounded-full tw-bg-theme-action-color/70 tw-opacity-75"></span>
                                            <span class="tw-relative tw-inline-flex tw-rounded-full tw-h-3 tw-w-3 tw-bg-theme-action-color"></span>
                                        </span>
                                    </a>
                                @elseif(!is_null($current_delivery_method))
                                    <livewire:theme.delivery-date/>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
            <div class="tw-sticky tw-z-20 tw-top-0 tw-w-full">
                @php($order = $openOrder)

                <div class="tw-bg-white">

                    <header class="tw-relative tw-z-20">
                        <nav aria-label="Top" class="tw-relative tw-shadow-sm">
                            @include('theme::_partials.header.partials.top-nav', compact('store_menu', 'main_menu', 'has_subscription', 'order'))
                        </nav>
                    </header>
                </div>
            </div>

            @include('theme::_partials.header.partials.bottom-nav', compact('main_menu'))

            @includeWhen(
                ($show_announcement_bar ?? true) && app('theme')->setting('show_announcement_bar'),
                'theme::_partials.header.partials.announcement-bar',
                ['announcement' => app('theme')->setting('announcement_bar_content')]
            )

            <!-- Start Site Messages -->
            @include('theme::_partials.errors')
            <!-- End Site Messages -->

            <!-- Start Main Content -->
            @yield('content')
            <!-- End Main Content -->

            <!-- Start Back To Top Link -->
            <div class="backToTopLink__container">
                <a href="#top-of-page" class="backToTopLink__link"><i class="fa fa-caret-up"></i> {{ __('Return To Top') }}
                </a>
            </div>
            <!-- End Back To Top Link -->

            <!-- Start Site Footer -->
            @include('theme::_partials.footer')
            <!-- End Main Content -->

            <a id="bottom-of-page"></a>

            @if($show_tripwire ?? true)
                @guest
                    <div class="tw-pt-[8.25rem] lg:tw-pt-[7.1rem]"></div>
                    <livewire:theme.offers.tripwire/>
                @endguest
            @endif

            @include('theme::_partials.header.partials.flyout-menu', compact('store_menu', 'main_menu'))

        </div>


    </div>
</div>

{{-- Cart slider --}}
<livewire:theme.side-panel/>
<livewire:theme.modal/>
<livewire:theme.product-search-command-palette :has_subscription="$has_subscription"
                                               :has_order="!is_null($openOrder)"/>
<livewire:theme.modals.modal/>

{{-- Address can be added from any page with main nav --}}
<livewire:theme.modals.add-address/>

@auth
    @if(empty(auth()->user()->pickup_point))
        <livewire:theme.modals.confirm-delivery-method/>
    @endif
@endauth

@guest
    <livewire:theme.modals.register-and-add-product/>
@endguest

<livewire:theme.storefront-notifications/>

@yield('modals')

<!-- Scripts -->
@livewireScriptConfig

@stack('scripts')

{!! setting('footer_scripts') !!}

@auth
    <script>
        if (typeof _dcq !== 'undefined') {
            _dcq.push(['identify', {
                email: "{{ auth()->user()->email }}",
                success: function(response) {
                    console.info('Subscriber identified in Drip');
                }
            }]);
        }
    </script>
@endauth

@if(Session::has('userWasCreated'))
    <script>
        if (typeof fbq !== 'undefined') {
            fbq('track', 'CompleteRegistration', {
                currency: 'USD',
                value: 0.50
            });
        }
    </script>
@endif

@if(session()->has('livewire_dispatch'))
    <script>
        document.addEventListener('livewire:initialized', () => {
            Livewire.dispatch(
                    @json(session('livewire_dispatch.event')),
                    @json(session('livewire_dispatch.params'))
            );
        });
    </script>
@endif

<!-- Cookie Consent Banner -->
<livewire:theme.cookie-consent />

</body>
</html>
