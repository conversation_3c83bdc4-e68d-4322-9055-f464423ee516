<div
    x-data="{ open: @entangle('showBanner') }"
    x-init="$watch('open', value => {
        if (value && typeof gtag !== 'undefined') {
            gtag('event', 'cookie_banner_viewed', {
                event_category: 'privacy',
                event_label: 'cookie_banner'
            });
        }
    })"
    class="tw-relative tw-z-50"
    role="banner"
    aria-labelledby="cookie-consent-title"
    aria-describedby="cookie-consent-description"
>
    <!-- Banner -->
    <div 
        x-show="open"
        x-cloak
        x-transition:enter="tw-ease-out tw-duration-300"
        x-transition:enter-start="tw-opacity-0 tw-translate-y-full"
        x-transition:enter-end="tw-opacity-100 tw-translate-y-0"
        x-transition:leave="tw-ease-in tw-duration-200"
        x-transition:leave-start="tw-opacity-100 tw-translate-y-0"
        x-transition:leave-end="tw-opacity-0 tw-translate-y-full"
        class="tw-fixed tw-inset-x-0 tw-bottom-0 tw-bg-gray-900 tw-text-white tw-shadow-lg tw-border-t-4 tw-border-theme-brand-color"
    >
        <div class="tw-max-w-7xl tw-mx-auto tw-px-4 sm:tw-px-6 lg:tw-px-8">
            <div class="tw-flex tw-flex-col sm:tw-flex-row tw-items-start sm:tw-items-center tw-justify-between tw-py-4 tw-gap-4">
                <!-- Message -->
                <div class="tw-flex-1">
                    <div class="tw-flex tw-items-start tw-space-x-3">
                        <!-- Cookie Icon -->
                        <div class="tw-flex-shrink-0 tw-mt-0.5">
                            <svg class="tw-h-5 tw-w-5 tw-text-theme-brand-color" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        
                        <!-- Content -->
                        <div class="tw-flex-1">
                            <p id="cookie-consent-description" class="tw-text-sm tw-leading-relaxed tw-m-0 tw-text-gray-100">
                                We use cookies to make your visit to our farm store even better. By continuing to browse, you're agreeing to our use of cookies. You can read more in our 
                                <a href="{{ route('page.show', 'privacy-policy') }}" 
                                   class="tw-text-theme-brand-color hover:tw-text-theme-brand-color/80 tw-underline tw-underline-offset-2 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-brand-color focus:tw-ring-offset-2 focus:tw-ring-offset-gray-900 tw-rounded tw-transition-colors tw-duration-200">
                                    Privacy Policy
                                </a>.
                            </p>
                        </div>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="tw-flex tw-items-center tw-space-x-3 tw-flex-shrink-0">
                    <!-- Got it Button -->
                    <button 
                        wire:click="acceptCookies"
                        wire:loading.attr="disabled"
                        wire:target="acceptCookies"
                        class="tw-bg-theme-brand-color hover:tw-bg-theme-brand-color/90 focus:tw-bg-theme-brand-color/90 tw-text-white tw-px-6 tw-py-2 tw-rounded-md tw-text-sm tw-font-medium tw-transition-colors tw-duration-200 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-brand-color focus:tw-ring-offset-2 focus:tw-ring-offset-gray-900 tw-shadow-sm disabled:tw-opacity-50 disabled:tw-cursor-not-allowed"
                        aria-label="Accept cookies and close banner"
                    >
                        <span wire:loading.remove wire:target="acceptCookies">Got it!</span>
                        <span wire:loading.inline wire:target="acceptCookies" class="tw-flex tw-items-center">
                            <svg class="tw-animate-spin tw--ml-1 tw-mr-2 tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
