import { defineStore } from 'pinia';
import { itemPrice } from '../composables/cart';
import { toRaw } from 'vue';

export const useCartProductStore = defineStore('cart', {
    state: () => ({
        type: null,
        date_id: null,
        delivery_method_id: null,
        items: [],
        notes: '',
        is_gift: false,
        recipient_email: '',
        recipient_notes: '',
        discounts: {
            coupons: [],
            gift_card: {
                name: '',
                code: '',
                amount: 0
            },
            store_credit: {
                amount: 0
            }
        },
        customer: {
            first_name: '',
            last_name: '',
            email: '',
            phone: '',
            save_for_later: true,
            opt_in_to_sms: false,
            subscribed_to_sms: false
        },
        shipping: {
            address_id: null,
            street: '',
            street_2: '',
            city: '',
            state: '',
            zip: '',
            country: '',
            save_for_later: true
        },
        billing: {
            method: null,
            source_id: null,
            save_for_later: true
        }
    }),

    getters: {
        contactInformationIsComplete: state => {
            return state.customer.first_name !== ''
                && state.customer.last_name !== ''
                && state.customer.email !== ''
                && state.customer.phone
                && (
                    !state.is_gift
                    || state.recipient_email !== ''
                );
            ;
        },

        shippingInformationIsComplete: state => {
            return !! state.shipping.street
                && !! state.shipping.city
                && !! state.shipping.state
                && !! state.shipping.zip;
        },

        billingInformationIsComplete: state => {
            if (state.billing.method === null) return false;

            if (state.billing.method !== 'card') return true;

            return state.billing.source_id !== null;
        },

        itemsArray(state) {
            return Object.values(state.items);
        },

        itemsSubtotal() {
            return this.itemsArray
                .reduce((previousValue, item) => previousValue + itemPrice(toRaw(item)), 0);
        },

        couponsSubtotal(state) {
            return 0;
        },

        creditTotal(state) {
            return state.discounts.store_credit.amount;
        },

        discountTotal(state) {
            return this.couponsSubtotal + state.discounts.gift_card.amount + this.creditTotal;
        },

        itemsAsGoogleArray(state) {
            return toRaw(state.items).map((item, index) => {
                return {
                    item_id: item.product.id,
                    item_name: item.product.title,
                    index: index,
                    item_category: item.product.category?.parent_category ? item.product.category.parent_category.name : item.product.category?.name,
                    item_category2: item.product.category ? item.product.category.name : null,
                    item_list_id: null,
                    item_list_name: null,
                    price: item.price / 100,
                    quantity: item.quantity
                };
            });
        }
    },

    actions: {
        toggleCustomerSaveForLater() {
            this.customer.save_for_later = !this.customer.save_for_later;
        },
        toggleCustomerSmsOptIn() {
            this.customer.opt_in_to_sms = !this.customer.opt_in_to_sms;
        },
        toggleShippingSaveForLater() {
            this.shipping.save_for_later = !this.shipping.save_for_later;
        },
        toggleBillingSaveForLater() {
            this.billing.save_for_later = !this.billing.save_for_later;
        },
        updateBillingMethod(method, source_id = null) {
            this.billing.method = method;
            this.billing.source_id = source_id;
        },
        updateQuantity(quantity) {
            const updatedItem = { ...this.items[0] };
            updatedItem.quantity = quantity;
            updatedItem.weight = parseFloat((updatedItem.product.weight * quantity).toFixed(2));
            this.items.splice(0, 1, updatedItem);
        },

        toggleGiftOptIn() {
            this.is_gift = !this.is_gift;
        }
    }
});
