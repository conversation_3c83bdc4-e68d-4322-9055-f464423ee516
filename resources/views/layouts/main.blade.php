<!DOCTYPE html>
<html lang="en">
<head>
    <meta name="ROBOTS" content="NOINDEX, NOFOLLOW">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta id="token" name="token" value="{{ csrf_token() }}">
    <title>{{ $pageTitle ?? 'GrazeCart' }}</title>
    {{--Style Sheets--}}
    <link href="{{ url('css/vendors.css') }}" rel="stylesheet">

    @livewireStyles
    @vite([
        'resources/assets/css/tailwind.css',
        'resources/assets/less/admin.less',
        'resources/assets/js/admin/main.js'
    ])
    <link href="{{ url('/css/fontawesome-all.min.css') }}" rel="stylesheet">
    @yield('styles')
    <link href='//fonts.googleapis.com/css?family=Open+Sans:400,500,600,700' rel='stylesheet' type='text/css'>
    <link rel="apple-touch-icon" sizes="57x57" href="/images/apple-icon-57x57.png">
    <link rel="apple-touch-icon" sizes="60x60" href="/images/apple-icon-60x60.png">
    <link rel="apple-touch-icon" sizes="72x72" href="/images/apple-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="76x76" href="/images/apple-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="114x114" href="/images/apple-icon-114x114.png">
    <link rel="apple-touch-icon" sizes="120x120" href="/images/apple-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="144x144" href="/images/apple-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="/images/apple-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/images/apple-icon-180x180.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/images/android-icon-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="/images/favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/favicon-16x16.png">
    <!--[if lt IE 11]>
    <p>Sorry, GrazeCart does not support IE 10 or older</p>
    <![endif]-->

    @include('partials.admin-google-analytics-tag')
</head>

<body>
<div id="app">
    {{--Progress bar--}}
    <div class="progress-bar" id="progressBar">
        <span></span>
    </div>
    <div class="sidebarLayoutContainer {{ Cookie::get('navigation_toggled') ? 'collapsed' : '' }}" id="sidebarLayoutContainer">

        <nav class="hidden-print">
            @if(auth()->user()->isEditor())
                @include('layouts.sidebar.navigation.editor')
            @else
                @include('layouts.sidebar.navigation.admin')
            @endif
        </nav>

        <div class="wrapper" id="wrapper">

            <div class="accountMenu">

            </div>

            {{--Toolbar--}}
            <div class="toolbar">
                <div class="toolbar__breadcrumbs">
                    <ul class="breadcrumb-toolbar">
                        @yield('toolbar-breadcrumb')
                    </ul>
                </div>
                <div class="toolbar__buttons flex">
                    @yield('toolbar-buttons')
                </div>
            </div>

            {{--Content--}}
            <div class="container" id="mainContentContainer">
                @include('partials.errors')

                @yield('content')
            </div>
        </div>
    </div>

    @include('partials.messenger')
</div>

<livewire:admin.modals.modal/>

@yield('modals')

<livewire:admin.admin-notifications/>

@yield('scripts_prepend')

<script src="{{ url('/js/vendor.js?id=1234') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.4/js/select2.min.js"></script>

@livewireScriptConfig

@yield('scripts')

<!-- Cookie Consent Banner -->
<livewire:theme.cookie-consent />

</body>
</html>

