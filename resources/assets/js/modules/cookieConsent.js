export default () => ({
    showBanner: false,
    consentCookieName: 'cookie_consent',
    consentCookieValue: 'accepted',
    consentCookieExpireDays: 365,

    init() {
        // Check if user has already given consent
        this.showBanner = !this.hasConsent();
    },

    hasConsent() {
        return this.getCookie(this.consentCookieName) === this.consentCookieValue;
    },

    acceptCookies() {
        this.setCookie(this.consentCookieName, this.consentCookieValue, this.consentCookieExpireDays);
        this.showBanner = false;
    },

    setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
    },

    getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
});
