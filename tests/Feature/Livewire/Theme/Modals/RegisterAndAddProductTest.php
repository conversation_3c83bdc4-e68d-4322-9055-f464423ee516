<?php

namespace Tests\Feature\Livewire\Theme\Modals;

use App\Contracts\Geocoder;
use App\Livewire\Theme\Modals\RegisterAndAddProduct;
use App\Models\Cart;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\User;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Event;
use Livewire\Livewire;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class RegisterAndAddProductTest extends TenantTestCase
{
    #[Test]
    public function it_can_render()
    {
        Livewire::test(RegisterAndAddProduct::class)
            ->assertSet('open', false)
            ->assertSet('postal_code', '')
            ->assertSet('email', '')
            ->assertSet('product_id', null)
            ->assertSet('product_metadata', [])
            ->assertOk()
            ->assertViewIs('theme::livewire.modals.register-and-add-product');
    }

    #[Test]
    public function it_can_open()
    {
        $product = Product::factory()->create();

        $modal = Livewire::test(RegisterAndAddProduct::class)
            ->assertSet('open', false)
            ->assertSet('product_id', null)
            ->assertSet('product_metadata', [])
            ->assertOk();

        $modal->dispatch(
            event: 'open-modal-register-and-add-product',
            product_id: $product->id,
            product_metadata: ['foo' => 'bar']
        )
            ->assertSet('open', true)
            ->assertSet('product_id', $product->id)
            ->assertSet('product_metadata', ['foo' => 'bar']);
    }

    #[Test]
    public function it_can_register_and_add_product(): void
    {
        Event::fake();

        $geocoded_address = GeocodedAddress::makeFromArray([
            'city' => 'Test City',
            'state' => 'TS',
            'country' => 'Test Country',
            'postal_code' => '12345',
        ]);

        $this->mock(Geocoder::class, function (MockInterface $mock) use ($geocoded_address) {
            $mock->shouldReceive('fromZipcode')
                ->with('12345')
                ->andReturn($geocoded_address);
        });

        $delivery_method = Pickup::factory()->create();

        $this->mock(DeliveryMethodService::class, function (MockInterface $mock) use ($geocoded_address, $delivery_method) {
            $mock->shouldReceive('deliveryZones')->andReturnSelf()
                ->shouldReceive('find')->with(\Mockery::on(fn(GeocodedAddress $address) => $address->postalCode === '12345'))
                ->andReturn(new Collection([$delivery_method]));
        });

        Livewire::test(RegisterAndAddProduct::class)
            ->set('timestamp', now()->subSeconds(10)->timestamp)
            ->set('postal_code', '12345')
            ->set('email', '<EMAIL>')
            ->call('submit')
            ->assertOk()
            ->assertDispatched('cartDeliveryMethodUpdated')
            ->assertSessionHas('open-cart-side-panel')
            ->assertSessionHas('open-cart-side-panel');

        $this->assertDatabaseHas(User::class, [
            'email' => '<EMAIL>',
            'pickup_point' => $delivery_method->id,
            'zip' => '12345',
        ]);

        $user = User::where('email', '<EMAIL>')->first();

        $this->assertDatabaseHas(Cart::class, [
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
        ]);

        $cart = Cart::where([
            'shopper_type' => User::class,
            'shopper_id' => $user->id,
        ])->first();

        $this->assertEquals($delivery_method->id, $cart->cartLocation()?->id);

        $this->assertEquals([
            'address_id' => null,
            'street' => '',
            'street_2' => '',
            'city' => 'Test City',
            'state' => 'TS',
            'zip' => '12345',
            'postal_code' => '12345',
            'country' => 'USA',
            'save_for_later' => true,
        ], $cart->getShippingInfo());
    }
}
