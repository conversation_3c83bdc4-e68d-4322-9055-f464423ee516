<?php

namespace Tests\Feature\Theme;

use Tests\TenantTestCase;

class CookieConsentTest extends TenantTestCase
{
    /** @test */
    public function cookie_consent_banner_is_displayed_on_homepage_for_first_time_visitors()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('x-data="cookieConsent()"', false);
        $response->assertSee('We use cookies to make your visit to our farm store even better');
        $response->assertSee('Got it!');
    }

    /** @test */
    public function cookie_consent_banner_is_displayed_on_store_pages_for_first_time_visitors()
    {
        $response = $this->get('/store');

        $response->assertStatus(200);
        $response->assertSee('x-data="cookieConsent()"', false);
        $response->assertSee('We use cookies to make your visit to our farm store even better');
        $response->assertSee('Got it!');
    }

    /** @test */
    public function cookie_consent_banner_contains_privacy_policy_link()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('Privacy Policy');
        $response->assertSee('route(\'page.show\', \'privacy-policy\')', false);
    }

    /** @test */
    public function cookie_consent_banner_has_proper_accessibility_attributes()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        $response->assertSee('role="banner"', false);
        $response->assertSee('aria-label="Cookie consent banner"', false);
        $response->assertSee('aria-label="Accept cookies and close banner"', false);
    }

    /** @test */
    public function cookie_consent_banner_is_responsive_with_proper_css_classes()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // Check for responsive classes
        $response->assertSee('tw-flex-col sm:tw-flex-row', false);
        $response->assertSee('tw-items-start sm:tw-items-center', false);
        $response->assertSee('tw-fixed tw-bottom-0', false);
        $response->assertSee('tw-z-50', false);
    }

    /** @test */
    public function cookie_consent_banner_has_proper_transitions()
    {
        $response = $this->get('/');

        $response->assertStatus(200);
        // Check for Alpine.js transition classes
        $response->assertSee('x-transition:enter="tw-transition tw-ease-out tw-duration-300"', false);
        $response->assertSee('x-transition:leave="tw-transition tw-ease-in tw-duration-200"', false);
    }

    /** @test */
    public function cookie_consent_banner_is_included_in_checkout_layout()
    {
        // Create a simple product and add to cart to access checkout
        $product = \App\Models\Product::factory()->create([
            'active' => true,
            'price' => 1000, // $10.00
        ]);

        $this->post('/cart/add', [
            'product_id' => $product->id,
            'quantity' => 1,
        ]);

        $response = $this->get('/checkout');

        $response->assertSee('x-data="cookieConsent()"', false);
        $response->assertSee('We use cookies to make your visit to our farm store even better');
    }

    /** @test */
    public function cookie_consent_component_is_properly_registered_in_theme_js()
    {
        $themeJsContent = file_get_contents(resource_path('assets/js/theme/theme.js'));

        $this->assertStringContainsString("import cookieConsent from '../modules/cookieConsent.js';", $themeJsContent);
        $this->assertStringContainsString("Alpine.data('cookieConsent', cookieConsent);", $themeJsContent);
    }

    /** @test */
    public function cookie_consent_module_exists_and_has_proper_structure()
    {
        $cookieConsentPath = resource_path('assets/js/modules/cookieConsent.js');
        
        $this->assertFileExists($cookieConsentPath);
        
        $content = file_get_contents($cookieConsentPath);
        
        // Check for required methods
        $this->assertStringContainsString('showBanner:', $content);
        $this->assertStringContainsString('init()', $content);
        $this->assertStringContainsString('hasConsent()', $content);
        $this->assertStringContainsString('acceptCookies()', $content);
        $this->assertStringContainsString('setCookie(', $content);
        $this->assertStringContainsString('getCookie(', $content);
    }

    /** @test */
    public function cookie_consent_cookie_name_is_excluded_from_encryption()
    {
        $appBootstrapContent = file_get_contents(base_path('bootstrap/app.php'));
        
        $this->assertStringContainsString("'cookie_consent'", $appBootstrapContent);
    }
}
